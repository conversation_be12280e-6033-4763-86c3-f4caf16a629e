// 课程类型定义
export interface Course {
  id: number;
  title: string;
  description: string;
  tags: string[];
  cover_image?: string;
  poster_url?: string;
  instructor?: string;
  created_at: Date;
  updated_at: Date;
}

// 课程章节类型定义
export interface CourseSection {
  id: number;
  title: string;
  duration: number;
  sort_order: number;
  is_free: boolean;
  video_url?: string;
  is_published: boolean;
  course_id: number;
  created_at: Date;
  updated_at: Date;
}
